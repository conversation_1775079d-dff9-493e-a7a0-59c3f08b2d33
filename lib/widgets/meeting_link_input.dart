import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/meeting_client.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';

class MeetingLinkInput extends StatefulWidget {
  final String? initialLink;

  const MeetingLinkInput({super.key, this.initialLink});

  @override
  State<MeetingLinkInput> createState() => _MeetingLinkInputState();
}

class _MeetingLinkInputState extends State<MeetingLinkInput> {
  final TextEditingController _urlController = TextEditingController();
  final TextEditingController _sintesyNameController = TextEditingController();
  final TextEditingController _botNameController = TextEditingController();
  final MeetingAPI _meetingAPI = MeetingAPI();
  final SintesyCrud _sintesyCrud = SintesyCrud();
  final ImagePicker _picker = ImagePicker();

  bool _isLoading = false;
  bool _showAdvancedOptions = false;
  File? _selectedImage;

  @override
  void initState() {
    super.initState();
    if (widget.initialLink != null) {
      _urlController.text = widget.initialLink!;
    }
    // Define nome padrão da sintesy
    _sintesyNameController.text = 'Gravação de Reunião';
  }

  @override
  void dispose() {
    _urlController.dispose();
    _sintesyNameController.dispose();
    _botNameController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      _showSnackBar('Erro ao selecionar imagem: $e');
    }
  }

  void _removeImage() {
    setState(() {
      _selectedImage = null;
    });
  }

  Future<void> _createMeetingSintesy() async {
    final url = _urlController.text.trim();
    final sintesyName = _sintesyNameController.text.trim();
    final botName = _botNameController.text.trim();

    if (url.isEmpty) {
      _showSnackBar('Por favor, insira o link da reunião');
      return;
    }

    if (!MeetingAPI.isValidMeetingUrl(url)) {
      _showSnackBar(
          'Link inválido. Use apenas links do Google Meet ou Microsoft Teams');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String? botImageType;
      String? botImageData;

      // Processa a imagem se selecionada
      if (_selectedImage != null) {
        final bytes = await _selectedImage!.readAsBytes();
        botImageData = base64Encode(bytes);

        final extension = _selectedImage!.path.split('.').last.toLowerCase();
        botImageType = 'image/$extension';
      }

      // Chama a API para criar a sintesy
      final response = await _meetingAPI.createFromMeeting(
        meetingUrl: url,
        sintesyName: sintesyName.isNotEmpty ? sintesyName : null,
        botName: botName.isNotEmpty ? botName : null,
        botImageType: botImageType,
        botImageData: botImageData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);

        // Cria o modelo local da sintesy
        final attendeeBot =
            AttendeeBotModel.fromJson(responseData['attendee_bot'] ?? {});

        // Se não veio attendeeBot da API, cria um para teste
        final finalAttendeeBot = attendeeBot.meetingUrl != null
            ? attendeeBot
            : AttendeeBotModel(
                meetingUrl: url,
                botStatus: 'awaiting_approval',
                avatar: null,
              );

        final sintesy = SintesyModel(
          name: responseData['name'] ?? sintesyName.isNotEmpty
              ? sintesyName
              : 'Gravação de Reunião',
          createdDate: DateTime.now(),
          source: MeetingAPI.getMeetingType(url),
          status: responseData['status'],
          attendeeBot: finalAttendeeBot,
        );

        // Salva localmente
        await _sintesyCrud.add(sintesy);

        // Atualiza o estado
        if (mounted) {
          final sintesyState =
              Provider.of<SintesyState>(context, listen: false);
          await sintesyState.sintesysRefresh();

          if (mounted) {
            Navigator.pop(context);
            _showSnackBar('Sintesy de reunião criada com sucesso!');
          }
        }
      } else {
        _showSnackBar('Erro ao criar sintesy: ${response.statusCode}');
      }
    } catch (e) {
      _showSnackBar('Erro ao criar sintesy: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Campo de URL principal
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: _urlController.text.isNotEmpty
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.tertiary,
                width: 1.0,
              ),
            ),
            child: TextField(
              controller: _urlController,
              decoration: InputDecoration(
                hintText: 'Cole o link da reunião',
                hintStyle: TextStyle(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.6),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              style: const TextStyle(fontSize: 14),
              keyboardType: TextInputType.url,
              enabled: !_isLoading,
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),

          if (_urlController.text.isNotEmpty && !_showAdvancedOptions) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                IconButton(
                  onPressed: _isLoading
                      ? null
                      : () {
                          setState(() {
                            _showAdvancedOptions = !_showAdvancedOptions;
                          });
                        },
                  icon: Icon(
                    Icons.settings,
                    size: 18,
                    color: _showAdvancedOptions
                        ? Theme.of(context).primaryColor
                        : Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.cancel_outlined),
                  color: Colors.grey,
                  onPressed: () {
                    setState(() {
                      _urlController.clear();
                      _showAdvancedOptions = false;
                    });
                  },
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: _isLoading ? null : _createMeetingSintesy,
                    child: const Text(
                      'Iniciar Gravação',
                      style: TextStyle(fontSize: 15),
                    ),
                  ),
                ),
              ],
            ),
          ],

          if (_showAdvancedOptions) ...[
            const SizedBox(height: 16),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Configurações',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.8),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Nome da sintesy
                    TextField(
                      controller: _sintesyNameController,
                      decoration: InputDecoration(
                        labelText: 'Nome da Sintesy',
                        labelStyle: const TextStyle(fontSize: 12),
                        hintText: 'Ex: Reunião de Planejamento',
                        hintStyle: const TextStyle(fontSize: 11),
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                      ),
                      style: const TextStyle(fontSize: 12),
                      enabled: !_isLoading,
                    ),

                    const SizedBox(height: 12),

                    // Nome do bot
                    TextField(
                      controller: _botNameController,
                      decoration: InputDecoration(
                                                labelText: 'Nome do Bot',
                        labelStyle: const TextStyle(fontSize: 12),
                        hintText: 'Sintesy',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                      ),
                      style: const TextStyle(fontSize: 12),
                      enabled: !_isLoading,
                    ),

                    const SizedBox(height: 12),

                    // Avatar do bot
                    if (_selectedImage != null) ...[
                      Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: Image.file(
                              _selectedImage!,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Avatar selecionado',
                              style: TextStyle(
                                fontSize: 11,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 0.6),
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: _removeImage,
                            icon: const Icon(Icons.close,
                                size: 16, color: Colors.red),
                          ),
                        ],
                      ),
                    ] else ...[
                      OutlinedButton.icon(
                        onPressed: _isLoading ? null : _pickImage,
                        icon: const Icon(Icons.image, size: 14),
                        label: const Text('Mudar Avatar',
                            style: TextStyle(fontSize: 11)),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                    ],

                    const SizedBox(height: 16),

                    // Botões de ação
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                          onPressed: () {
                            setState(() {
                              _showAdvancedOptions = false;
                              _urlController.clear();
                              _sintesyNameController.clear();
                              _botNameController.clear();
                              _selectedImage = null;
                            });
                          },
                          child: Text(
                            'Cancelar',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.error,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        TextButton(
                          onPressed: _isLoading ? null : _createMeetingSintesy,
                          child: _isLoading
                              ? const SizedBox(
                                  height: 12,
                                  width: 12,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 1),
                                )
                              : Text(
                                  'Criar Sintesy',
                                  style: TextStyle(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
